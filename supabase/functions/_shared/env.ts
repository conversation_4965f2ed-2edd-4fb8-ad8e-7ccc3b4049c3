
const envSchema = z.object({
  SUPABASE_URL: z.string(),
  SUPABASE_SERVICE_ROLE_KEY: z.string(),
  SUPABASE_ANON_KEY: z.string(),
  ZOHO_ACCOUNTS_URL: z.string(),
  ZOHO_API_URL: z.string(),
  ZOHO_REFRESH_TOKEN: z.string(),
  ZOHO_CLIENT_ID: z.string(),
  ZOHO_CLIENT_SECRET: z.string(),
  STRIPE_PUBLIC_KEY: z.string(),
  STRIPE_SECRET_KEY: z.string(),
  STRIPE_SIGNING_SECRET: z.string(),
  ORIGIN_URL: z.string(),
  SENDGRID_API_KEY: z.string(),
  SENDGRID_SENDER_EMAIL: z.string(),
});

const parsedResults = envSchema.safeParse(Deno.env.toObject());

if (!parsedResults.success) {
  console.error(parsedResults.error);
  throw new Error(parsedResults.error.message);
}

export const env = parsedResults.data;
